from rest_framework.views import APIView
import pandas as pd
from google.cloud import bigquery
from rest_framework import serializers
from rest_framework.response import Response

from picking.models import PickOrder


# BigQuery schema definitions
ORDER_ITEMS_SCHEMA = [
    bigquery.<PERSON>hema<PERSON>ield("id", "INTEGER", mode="NULLABLE"),
    bigquery.SchemaField("sku", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("name", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("number", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField("status", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("skutype", "INTEGER", mode="NULLABLE"),
    bigquery.SchemaField("discount", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("mfg_date", "INTEGER", mode="NULLABLE"),
    bigquery.SchemaField("unittext", "STRING", mode="NULLABLE"),
    bigquery.Schema<PERSON>ield("productid", "INTEGER", mode="NULLABLE"),
    bigquery.SchemaField("totalprice", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField("producttype", "INTEGER", mode="NULLABLE"),
    bigquery.SchemaField("sku_barcode", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("serialnolist", "INTEGER", mode="REPEATED"),
    bigquery.SchemaField("discountamount", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField("eso_vatpercent", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField("original_price", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField("pricepernumber", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField(
        "return_quantity", "FLOAT", mode="NULLABLE", default_value_expression="0"
    ),
    bigquery.SchemaField("seller_discount", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField("shipping_amount", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField("platform_discount", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField("original_shipping_amount", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField("seller_shipping_discount", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField("platform_shipping_discount", "FLOAT", mode="NULLABLE"),
    bigquery.SchemaField("order_id", "INTEGER", mode="NULLABLE"),
]


def prepare_order_items_data(df_items):
    """
    Prepare order items DataFrame to match BigQuery schema.
    Ensures data types and handles missing columns.
    """
    # Create a copy to avoid modifying the original
    df = df_items.copy()

    # Define expected columns and their types
    schema_mapping = {
        "id": "Int64",
        "sku": "string",
        "name": "string",
        "number": "float64",
        "status": "string",
        "skutype": "Int64",
        "discount": "string",
        "mfg_date": "Int64",
        "unittext": "string",
        "productid": "Int64",
        "totalprice": "float64",
        "producttype": "Int64",
        "sku_barcode": "string",
        "serialnolist": "object",  # Will handle REPEATED field separately
        "discountamount": "float64",
        "eso_vatpercent": "float64",
        "original_price": "float64",
        "pricepernumber": "float64",
        "return_quantity": "float64",
        "seller_discount": "float64",
        "shipping_amount": "float64",
        "platform_discount": "float64",
        "original_shipping_amount": "float64",
        "seller_shipping_discount": "float64",
        "platform_shipping_discount": "float64",
        "order_id": "Int64",
    }

    # Add missing columns with None values
    for col in schema_mapping.keys():
        if col not in df.columns:
            df[col] = None

    # Convert data types
    for col, dtype in schema_mapping.items():
        if col in df.columns:
            try:
                if dtype == "string":
                    df[col] = df[col].astype("string")
                elif dtype == "Int64":
                    df[col] = pd.to_numeric(df[col], errors="coerce").astype("Int64")
                elif dtype == "float64":
                    df[col] = pd.to_numeric(df[col], errors="coerce").astype("float64")
                elif col == "serialnolist":
                    # Handle REPEATED field - ensure it's a list of integers
                    def convert_serialnolist(val):
                        if pd.isna(val) or val is None:
                            return []
                        if isinstance(val, list):
                            return [int(x) for x in val if pd.notna(x)]
                        if isinstance(val, str):
                            try:
                                # Try to parse as JSON array
                                import json

                                parsed = json.loads(val)
                                if isinstance(parsed, list):
                                    return [int(x) for x in parsed if pd.notna(x)]
                            except (json.JSONDecodeError, ValueError, TypeError):
                                pass
                        return []

                    df[col] = df[col].apply(convert_serialnolist)
            except Exception as e:
                print(f"Warning: Could not convert column {col} to {dtype}: {e}")

    # Set default value for return_quantity if it's null
    df["return_quantity"] = df["return_quantity"].fillna(0.0)

    # Ensure we only have the columns defined in the schema
    df = df[list(schema_mapping.keys())]

    return df


class BigQueryOrderImportAPI(APIView):
    authentication_classes = []
    permission_classes = []

    class Validator(serializers.Serializer):
        start_date = serializers.DateTimeField()
        end_date = serializers.DateTimeField()
        company = serializers.IntegerField(allow_null=True, required=False)

    def post(self, request, *args, **kwargs):
        """
        Handle POST requests to import orders into BigQuery.
        """
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data
        start_date = data["start_date"]
        end_date = data["end_date"]
        company = data.get("company")

        # BigQuery setup
        BQ_DATASET = "dobybot.report_api_tester"
        STG_ORDERS_TABLE = f"{BQ_DATASET}.staging_orders"
        STG_ITEMS_TABLE = f"{BQ_DATASET}.staging_order_items"
        ORDERS_TABLE = f"{BQ_DATASET}.orders"
        ITEMS_TABLE = f"{BQ_DATASET}.order_items"
        bq_client = bigquery.Client()

        # 1. fetch raw JSON rows
        pick_orders = PickOrder.objects.filter(
            update_date__gte=start_date,
            update_date__lte=end_date,
        )

        if company:
            pick_orders = pick_orders.filter(company=company)

        orders_list = []
        items_list = []

        # 2. deserialize and flatten
        for rec in pick_orders.values("id", "order_json"):
            od = rec["order_json"]
            # extract top-level order fields
            order_record = {
                k: od[k] for k in od if k not in ("list", "payments", "extra")
            }
            order_record["source_id"] = rec["id"]
            orders_list.append(order_record)

            # extract line items
            for item in od["list"]:
                item_flat = item.copy()
                item_flat["order_id"] = od["id"]
                items_list.append(item_flat)

        df_orders = pd.DataFrame(orders_list)
        df_items = pd.DataFrame(items_list)

        # 2. Load into staging tables, truncating them:
        # Load orders table
        orders_job_cfg = bigquery.LoadJobConfig(
            write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE
        )
        orders_load_job = bq_client.load_table_from_dataframe(
            df_orders, STG_ORDERS_TABLE, job_config=orders_job_cfg
        )
        orders_load_job.result()

        # Prepare items data to match schema and load items table with specific schema
        df_items_prepared = prepare_order_items_data(df_items)
        items_job_cfg = bigquery.LoadJobConfig(
            write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
            schema=ORDER_ITEMS_SCHEMA,
        )
        items_load_job = bq_client.load_table_from_dataframe(
            df_items_prepared, STG_ITEMS_TABLE, job_config=items_job_cfg
        )
        items_load_job.result()

        # 3. MERGE staging → production (dedupe on order_id/[item_id]):
        merge_orders_sql = f"""
        MERGE `{ORDERS_TABLE}` AS target
        USING `{STG_ORDERS_TABLE}` AS src
        ON target.number = src.number
        WHEN MATCHED THEN
        UPDATE SET
            target.tag = src.tag,
            target.line = src.line,
            target.isCOD = src.isCOD,
            target.amount = src.amount,
            target.freeze = src.freeze,
            target.lineid = src.lineid,
            target.number = src.number,
            target.remark = src.remark,
            target.status = src.status,
            target.vattype = src.vattype,
            target.version = src.version,
            target.discount = src.discount,
            target.createdby = src.createdby,
            target.ordertype = src.ordertype,
            target.reference = src.reference,
            target.sharelink = src.sharelink,
            target.vatamount = src.vatamount,
            target.customerid = src.customerid,
            target.facebookid = src.facebookid,
            target.trackingno = src.trackingno,
            target.vatpercent = src.vatpercent,
            target.description = src.description,
            target.shippingvat = src.shippingvat,
            target.createuserid = src.createuserid,
            target.customercode = src.customercode,
            target.customername = src.customername,
            target.facebookname = src.facebookname,
            target.saleschannel = src.saleschannel,
            target.shippingname = src.shippingname,
            target.amount_pretax = src.amount_pretax,
            target.customeremail = src.customeremail,
            target.customerphone = src.customerphone,
            target.paymentamount = src.paymentamount,
            target.paymentmethod = src.paymentmethod,
            target.paymentstatus = src.paymentstatus,
            target.remark_status = src.remark_status,
            target.return_status = src.return_status,
            target.shippingemail = src.shippingemail,
            target.shippingphone = src.shippingphone,
            target.voucheramount = src.voucheramount,
            target.warehousecode = src.warehousecode,
            target.createusername = src.createusername,
            target.discountamount = src.discountamount,
            target.sellerdiscount = src.sellerdiscount,
            target.shippingamount = src.shippingamount,
            target.customeraddress = src.customeraddress
        WHEN NOT MATCHED THEN
        INSERT (
            id, tag, line, isCOD, amount, freeze, lineid, number, remark, status,
            vattype, version, discount, createdby, ordertype, reference, sharelink,
            vatamount, customerid, facebookid, trackingno, vatpercent, description,
            shippingvat, createuserid, customercode, customername, facebookname,
            saleschannel, shippingname, amount_pretax, customeremail, customerphone,
            paymentamount, paymentmethod, paymentstatus, remark_status, return_status,
            shippingemail, shippingphone, voucheramount, warehousecode, createusername,
            discountamount, sellerdiscount, shippingamount, customeraddress
        ) VALUES (
            src.id, src.tag, src.line, src.isCOD, src.amount, src.freeze, src.lineid,
            src.number, src.remark, src.status, src.vattype, src.version, src.discount,
            src.createdby, src.ordertype, src.reference, src.sharelink, src.vatamount,
            src.customerid, src.facebookid, src.trackingno, src.vatpercent, src.description,
            src.shippingvat, src.createuserid, src.customercode, src.customername,
            src.facebookname, src.saleschannel, src.shippingname, src.amount_pretax,
            src.customeremail, src.customerphone, src.paymentamount, src.paymentmethod,
            src.paymentstatus, src.remark_status, src.return_status, src.shippingemail,
            src.shippingphone, src.voucheramount, src.warehousecode, src.createusername,
            src.discountamount, src.sellerdiscount, src.shippingamount, src.customeraddress
        )
        """

        merge_items_sql = f"""
        MERGE `{ITEMS_TABLE}` AS target
        USING `{STG_ITEMS_TABLE}` AS src
        ON target.order_id = src.order_id
        AND target.sku = src.sku
        WHEN MATCHED THEN
        UPDATE SET
            target.sku = src.sku,
            target.name = src.name,
            target.number = src.number,
            target.status = src.status,
            target.skutype = src.skutype,
            target.discount = src.discount,
            target.mfg_date = src.mfg_date,
            target.unittext = src.unittext,
            target.productid = src.productid,
            target.totalprice = src.totalprice,
            target.producttype = src.producttype,
            target.sku_barcode = src.sku_barcode,
            target.serialnolist = src.serialnolist,
            target.discountamount = src.discountamount,
            target.eso_vatpercent = src.eso_vatpercent,
            target.original_price = src.original_price,
            target.pricepernumber = src.pricepernumber,
            target.return_quantity = src.return_quantity,
            target.seller_discount = src.seller_discount,
            target.shipping_amount = src.shipping_amount,
            target.platform_discount = src.platform_discount,
            target.original_shipping_amount = src.original_shipping_amount,
            target.seller_shipping_discount = src.seller_shipping_discount,
            target.platform_shipping_discount = src.platform_shipping_discount
        WHEN NOT MATCHED THEN
        INSERT (
            id, sku, name, number, status, skutype, discount, mfg_date, unittext,
            productid, totalprice, producttype, sku_barcode, serialnolist,
            discountamount, eso_vatpercent, original_price, pricepernumber,
            return_quantity, seller_discount, shipping_amount, platform_discount,
            original_shipping_amount, seller_shipping_discount, platform_shipping_discount,
            order_id
        ) VALUES (
            src.id, src.sku, src.name, src.number, src.status, src.skutype,
            src.discount, src.mfg_date, src.unittext, src.productid, src.totalprice,
            src.producttype, src.sku_barcode, src.serialnolist, src.discountamount,
            src.eso_vatpercent, src.original_price, src.pricepernumber, src.return_quantity,
            src.seller_discount, src.shipping_amount, src.platform_discount,
            src.original_shipping_amount, src.seller_shipping_discount,
            src.platform_shipping_discount, src.order_id
        )
        """

        bq_client.query(merge_orders_sql).result()
        bq_client.query(merge_items_sql).result()

        return Response(
            {
                "status": "success",
                "message": f"Loaded {len(df_orders)} orders & {len(df_items)} items.",
            },
            status=200,
        )
