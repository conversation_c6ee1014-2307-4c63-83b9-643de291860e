from rest_framework.views import APIView
import pandas as pd
from google.cloud import bigquery
from rest_framework import serializers
from rest_framework.response import Response

from picking.models import PickOrder


class BigQueryOrderImportAPI(APIView):
    authentication_classes = []
    permission_classes = []

    class Validator(serializers.Serializer):
        start_date = serializers.DateTimeField()
        end_date = serializers.DateTimeField()
        company = serializers.IntegerField(allow_null=True, required=False)

    def post(self, request, *args, **kwargs):
        """
        Handle POST requests to import orders into BigQuery.
        """
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data
        start_date = data["start_date"]
        end_date = data["end_date"]
        company = data.get("company")

        # BigQuery setup
        BQ_DATASET = "dobybot.report_api_tester"
        STG_ORDERS_TABLE = f"{BQ_DATASET}.staging_orders"
        STG_ITEMS_TABLE = f"{BQ_DATASET}.staging_order_items"
        ORDERS_TABLE = f"{BQ_DATASET}.orders"
        ITEMS_TABLE = f"{BQ_DATASET}.order_items"
        bq_client = bigquery.Client()

        # 1. fetch raw JSON rows
        pick_orders = PickOrder.objects.filter(
            update_date__gte=start_date,
            update_date__lte=end_date,
        )

        if company:
            pick_orders = pick_orders.filter(company=company)

        orders_list = []
        items_list = []

        # 2. deserialize and flatten
        for rec in pick_orders.values("id", "order_json"):
            od = rec["order_json"]
            # extract top-level order fields
            order_record = {
                k: od[k] for k in od if k not in ("list", "payments", "extra")
            }
            order_record["source_id"] = rec["id"]
            orders_list.append(order_record)

            # extract line items
            for item in od["list"]:
                item_flat = item.copy()
                item_flat["order_id"] = od["id"]
                items_list.append(item_flat)

        df_orders = pd.DataFrame(orders_list)
        df_items = pd.DataFrame(items_list)

        # 2. Load into staging tables, truncating them:
        for df, table in [(df_orders, STG_ORDERS_TABLE), (df_items, STG_ITEMS_TABLE)]:
            job_cfg = bigquery.LoadJobConfig(
                write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE
            )
            load_job = bq_client.load_table_from_dataframe(
                df, table, job_config=job_cfg
            )
            load_job.result()

        # 3. MERGE staging → production (dedupe on order_id/[item_id]):
        merge_orders_sql = f"""
        MERGE `{ORDERS_TABLE}` AS target
        USING `{STG_ORDERS_TABLE}` AS src
        ON target.number = src.number
        WHEN MATCHED THEN
        UPDATE SET 
            target.tag = src.tag,
            target.line = src.line,
            target.isCOD = src.isCOD,
            target.amount = src.amount,
            target.freeze = src.freeze,
            target.lineid = src.lineid,
            target.number = src.number,
            target.remark = src.remark,
            target.status = src.status,
            target.vattype = src.vattype,
            target.version = src.version,
            target.discount = src.discount,
            target.createdby = src.createdby,
            target.ordertype = src.ordertype,
            target.reference = src.reference,
            target.sharelink = src.sharelink,
            target.vatamount = src.vatamount,
            target.customerid = src.customerid,
            target.facebookid = src.facebookid,
            target.trackingno = src.trackingno,
            target.vatpercent = src.vatpercent,
            target.description = src.description,
            target.shippingvat = src.shippingvat,
            target.createuserid = src.createuserid,
            target.customercode = src.customercode,
            target.customername = src.customername,
            target.facebookname = src.facebookname,
            target.saleschannel = src.saleschannel,
            target.shippingname = src.shippingname,
            target.amount_pretax = src.amount_pretax,
            target.customeremail = src.customeremail,
            target.customerphone = src.customerphone,
            target.paymentamount = src.paymentamount,
            target.paymentmethod = src.paymentmethod,
            target.paymentstatus = src.paymentstatus,
            target.remark_status = src.remark_status,
            target.return_status = src.return_status,
            target.shippingemail = src.shippingemail,
            target.shippingphone = src.shippingphone,
            target.voucheramount = src.voucheramount,
            target.warehousecode = src.warehousecode,
            target.createusername = src.createusername,
            target.discountamount = src.discountamount,
            target.sellerdiscount = src.sellerdiscount,
            target.shippingamount = src.shippingamount,
            target.customeraddress = src.customeraddress
        WHEN NOT MATCHED THEN
        INSERT (
            id, tag, line, isCOD, amount, freeze, lineid, number, remark, status, 
            vattype, version, discount, createdby, ordertype, reference, sharelink, 
            vatamount, customerid, facebookid, trackingno, vatpercent, description, 
            shippingvat, createuserid, customercode, customername, facebookname, 
            saleschannel, shippingname, amount_pretax, customeremail, customerphone, 
            paymentamount, paymentmethod, paymentstatus, remark_status, return_status, 
            shippingemail, shippingphone, voucheramount, warehousecode, createusername, 
            discountamount, sellerdiscount, shippingamount, customeraddress
        ) VALUES (
            src.id, src.tag, src.line, src.isCOD, src.amount, src.freeze, src.lineid, 
            src.number, src.remark, src.status, src.vattype, src.version, src.discount, 
            src.createdby, src.ordertype, src.reference, src.sharelink, src.vatamount, 
            src.customerid, src.facebookid, src.trackingno, src.vatpercent, src.description, 
            src.shippingvat, src.createuserid, src.customercode, src.customername, 
            src.facebookname, src.saleschannel, src.shippingname, src.amount_pretax, 
            src.customeremail, src.customerphone, src.paymentamount, src.paymentmethod, 
            src.paymentstatus, src.remark_status, src.return_status, src.shippingemail, 
            src.shippingphone, src.voucheramount, src.warehousecode, src.createusername, 
            src.discountamount, src.sellerdiscount, src.shippingamount, src.customeraddress
        )
        """

        merge_items_sql = f"""
        MERGE `{ITEMS_TABLE}` AS target
        USING `{STG_ITEMS_TABLE}` AS src
        ON target.order_id = src.order_id
        AND target.sku = src.sku
        WHEN MATCHED THEN
        UPDATE SET
            target.sku = src.sku,
            target.name = src.name,
            target.number = src.number,
            target.status = src.status,
            target.skutype = src.skutype,
            target.discount = src.discount,
            target.mfg_date = src.mfg_date,
            target.unittext = src.unittext,
            target.productid = src.productid,
            target.totalprice = src.totalprice,
            target.producttype = src.producttype,
            target.sku_barcode = src.sku_barcode,
            target.serialnolist = src.serialnolist,
            target.discountamount = src.discountamount,
            target.eso_vatpercent = src.eso_vatpercent,
            target.original_price = src.original_price,
            target.pricepernumber = src.pricepernumber,
            target.return_quantity = src.return_quantity,
            target.seller_discount = src.seller_discount,
            target.shipping_amount = src.shipping_amount,
            target.platform_discount = src.platform_discount,
            target.original_shipping_amount = src.original_shipping_amount,
            target.seller_shipping_discount = src.seller_shipping_discount,
            target.platform_shipping_discount = src.platform_shipping_discount
        WHEN NOT MATCHED THEN
        INSERT (
            id, sku, name, number, status, skutype, discount, mfg_date, unittext, 
            productid, totalprice, producttype, sku_barcode, serialnolist, 
            discountamount, eso_vatpercent, original_price, pricepernumber, 
            return_quantity, seller_discount, shipping_amount, platform_discount, 
            original_shipping_amount, seller_shipping_discount, platform_shipping_discount, 
            order_id
        ) VALUES (
            src.id, src.sku, src.name, src.number, src.status, src.skutype, 
            src.discount, src.mfg_date, src.unittext, src.productid, src.totalprice, 
            src.producttype, src.sku_barcode, src.serialnolist, src.discountamount, 
            src.eso_vatpercent, src.original_price, src.pricepernumber, src.return_quantity, 
            src.seller_discount, src.shipping_amount, src.platform_discount, 
            src.original_shipping_amount, src.seller_shipping_discount, 
            src.platform_shipping_discount, src.order_id
        )
        """

        bq_client.query(merge_orders_sql).result()
        bq_client.query(merge_items_sql).result()

        return Response(
            {
                "status": "success",
                "message": f"Loaded {len(df_orders)} orders & {len(df_items)} items.",
            },
            status=200,
        )
